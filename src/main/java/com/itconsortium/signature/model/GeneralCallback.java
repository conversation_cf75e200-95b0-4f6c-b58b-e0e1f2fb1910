package com.itconsortium.signature.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.stereotype.Component;

@Data
@Component
public class GeneralCallback {
    private String uniwalletTransactionId;
    private String networkTransactionId;
    private String responseCode;
    private String responseMessage;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String narration;
}
