//package com.itconsortium.signature.utils;
//
//
//import org.apache.xml.security.Init;
//import org.apache.xml.security.signature.XMLSignature;
//import org.apache.xml.security.transforms.Transform;
//import org.apache.xml.security.transforms.Transforms;
//import org.apache.xml.security.transforms.params.XPath2FilterContainer;
//import org.apache.xml.security.keys.KeyInfo;
//import org.apache.xml.security.utils.Constants;
//import org.w3c.dom.Document;
//import org.xml.sax.InputSource;
//
//import javax.xml.parsers.DocumentBuilderFactory;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.InputStream;
//import java.io.StringReader;
//import java.nio.file.Files;
//import java.security.KeyFactory;
//import java.security.KeyStore;
//import java.security.PrivateKey;
//import java.security.cert.CertificateFactory;
//import java.security.cert.X509Certificate;
//import java.security.spec.PKCS8EncodedKeySpec;
//import java.util.Base64;
//import javax.xml.transform.Transformer;
//import javax.xml.transform.TransformerFactory;
//import javax.xml.transform.dom.DOMSource;
//import javax.xml.transform.stream.StreamResult;
//
//public class XPathMultiReferenceSignature {
//    public static final String XPath2Filter = "http://www.w3.org/2002/06/xmldsig-filter2";
//    public static final String ALGO_ID_DIGEST_SHA256 = "http://www.w3.org/2001/04/xmlenc#sha256";
//
//    private static Document parseXml(String xml) throws Exception {
//        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
//        dbf.setNamespaceAware(true);
//        dbf.setIgnoringElementContentWhitespace(true);
////        dbf.setValidating(true);
////        dbf.setIgnoringElementContentWhitespace(true);
//        return dbf.newDocumentBuilder().parse(new InputSource(new StringReader(xml)));
//    }
//
//    private static PrivateKey loadPrivateKey(String filename) throws Exception {
//        byte[] keyBytes = Files.readAllBytes(new File(filename).toPath());
//        String pem = new String(keyBytes);
//
//        if (pem.contains("-----BEGIN PRIVATE KEY-----")) {
//            pem = pem.replace("-----BEGIN PRIVATE KEY-----", "")
//                    .replace("-----END PRIVATE KEY-----", "")
//                    .replaceAll("\\s", "");
//            keyBytes = Base64.getDecoder().decode(pem);
//        }
//
//        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
//        KeyFactory kf = KeyFactory.getInstance("RSA");
//        return kf.generatePrivate(spec);
//    }
//
//    private static X509Certificate loadCertificate(String filename) throws Exception {
//        CertificateFactory cf = CertificateFactory.getInstance("X.509");
//        try (InputStream in = new FileInputStream(filename)) {
//            return (X509Certificate) cf.generateCertificate(in);
//        }
//    }
//
//
//    public static void main(String[] args) throws Exception {
//        // Initialize Apache Santuario Library
//        Init.init();
//
//        // Load XML Document
//        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
//        dbf.setNamespaceAware(true);
////        Document doc = parseXml(RNDPSSignatureGenerator.getXMLMessage());
//
//        // Load KeyStore (PKCS12 Example)
////        KeyStore ks = KeyStore.getInstance("PKCS12");
////        ks.load(new FileInputStream("keystore.p12"), "keystorePassword".toCharArray());
////        String alias = ks.aliases().nextElement();
//
//        PrivateKey privateKey = loadPrivateKey("/Users/<USER>/Documents/aws_local_repo/rswitch_signature/src/main/resources/private_key.key");
////        PrivateKey publicKey = loadPublicKey("/Users/<USER>/Documents/aws_local_repo/rswitch_signature/src/main/resources/rswitch.etransflow.io.key");
//        X509Certificate cert = loadCertificate("/Users/<USER>/Documents/aws_local_repo/rswitch_signature/src/main/resources/rswitch.etransflow.io.crt");
//
//        // Create XMLSignature Object
//        XMLSignature sig = new XMLSignature(doc, null, XMLSignature.ALGO_ID_SIGNATURE_RSA_SHA256);
//
//        // Append Signature Node to Document
//        doc.getDocumentElement().appendChild(sig.getElement());
//
//        // Transforms for Reference with XPath2 Filter
//        Transforms transforms = new Transforms(doc);
//        // XPath Filter to select Header
//        transforms.addTransform(ALGO_ID_DIGEST_SHA256,
//                XPath2FilterContainer.newInstanceIntersect(doc, "//AppHdr").getElement());
//        // XPath Filter to select Customer
//        transforms.addTransform(ALGO_ID_DIGEST_SHA256,
//                XPath2FilterContainer.newInstanceIntersect(doc, "//Document").getElement());
//
//        // Add Reference to Signature
//        sig.addDocument("", transforms, ALGO_ID_DIGEST_SHA256);
//
//        // Add KeyInfo (Certificate)
//        sig.addKeyInfo(cert);
//        sig.addKeyInfo(cert.getPublicKey());
//
//        // Sign the Document
//        sig.sign(privateKey);
//
//        // Output Signed XML
//        String xmlOutput = RNDPSSignatureGenerator.transformToString(doc);
//        System.out.println(xmlOutput);
////        TransformerFactory tf = TransformerFactory.newInstance();
////        Transformer trans = tf.newTransformer();
////        trans.transform(new DOMSource(doc), new StreamResult(System.out));
//    }
//}
