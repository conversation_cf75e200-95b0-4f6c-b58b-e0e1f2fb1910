package com.itconsortium.signature.model;

import lombok.Data;
import org.springframework.stereotype.Component;

@Data
@Component
public class MobileMoneyCallback {
    private String refNo;
    private String networkTransactionId;
    private String responseCode;
    private String responseMessage;


    public GeneralCallback buildGeneralCallback() {
        GeneralCallback uniwalletCallback = new GeneralCallback();
        uniwalletCallback.setResponseCode(this.responseCode);
        uniwalletCallback.setResponseMessage(this.responseMessage);
        uniwalletCallback.setUniwalletTransactionId(this.refNo);
        uniwalletCallback.setNetworkTransactionId(this.networkTransactionId);
        return uniwalletCallback;
    }
}
