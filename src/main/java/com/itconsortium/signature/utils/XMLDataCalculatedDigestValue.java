package com.itconsortium.signature.utils;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.KeyValue;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.UUID;

@AllArgsConstructor
@Service
public class XMLDataCalculatedDigestValue {

    public String sign(String xmlData, String elementName, String keyInfoId, ApplicationProperties applicationProperties) throws Exception {

        //Retrieve Keys from properties
        PrivateKey privateKey = loadPrivateKey("/Users/<USER>/Documents/aws_local_repo/rswitch_signature/src/main/resources/private_key.key");
        X509Certificate cert = loadCertificate("/Users/<USER>/Documents/aws_local_repo/rswitch_signature/src/main/resources/rswitch.etransflow.io.crt");

        //Get Document from XML Data
        Document document = parseXml(xmlData.trim());

        // Create signature factory
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        //Get ID tag name of xml object being referenced for signing
        String assertionId = getIdName(document, elementName);
//        System.out.println("assertionId : "+ assertionId);

//        keyInfoId = "8f503934-1799-46d4-bd4c-66e44301209f";

        String docId = UUID.randomUUID().toString();

        Reference keyInfoRef = getReference(fac, "KEYINFO", applicationProperties.getKeyInfoURI(), keyInfoId);
        Reference xmlObjectReferenced = getReference(fac, "HEADER", applicationProperties.getHeaderRefURI(), assertionId);
        Reference signedBodyRef = getReference(fac, "DOC", applicationProperties.getDocURI(), docId);

        // Create KeyInfo with RSAKeyValue and X509Certificate
        KeyInfo ki = getKeyInfo(fac, cert, keyInfoId);

        Element docSignPath = getAppHdrElement(document);
        // Sign the document
        DOMSignContext dsc = new DOMSignContext(privateKey, docSignPath);
        dsc.setDefaultNamespacePrefix("ds");
        dsc.putNamespacePrefix("http://www.w3.org/2000/09/xmldsig#", "ds");

        // Create SignedInfo
        SignedInfo si = fac.newSignedInfo(
                fac.newCanonicalizationMethod(CanonicalizationMethod.EXCLUSIVE, (C14NMethodParameterSpec) null),
                fac.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null),
                Arrays.asList(keyInfoRef, xmlObjectReferenced, signedBodyRef)
        );

        // Create and sign the signature
        XMLSignature signature = fac.newXMLSignature(si, ki);
        signature.sign(dsc);

        // Output the signed XML with proper formatting
        String signedXml = transformToString(document);
//        String signedXml = document;
//      String signedXml = doc.getXmlEncoding();
//        System.out.println(signedXml);
        return signedXml;

    }

    private static String getIdName(Document document, String elementName) throws XPathExpressionException {
        XPathFactory xPathfactory = XPathFactory.newInstance();
        XPath xpath = xPathfactory.newXPath();
        XPathExpression xmlExprAssertionID = xpath.compile("//*[local-name()='"+ elementName +"']//@ID");
        String xmlId = (String) xmlExprAssertionID.evaluate(document, XPathConstants.STRING);
        return xmlId;
    }

        private static Element getAppHdrElement(Document doc)  {
        NodeList appHdrList = doc.getElementsByTagNameNS("urn:iso:std:iso:20022:tech:xsd:head.001.001.01", "AppHdr");
        if (appHdrList.getLength() == 0) {
            throw new RuntimeException("AppHdr element not found");
        }
        return (Element) appHdrList.item(0);
    }

    private static Document parseXml(String xml) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
//        dbf.setIgnoringElementContentWhitespace(true);
//        dbf.setValidating(true);
        return dbf.newDocumentBuilder().parse(new InputSource(new StringReader(xml)));
    }

    public static PrivateKey loadPrivateKey(String filename) throws Exception {
        byte[] keyBytes = Files.readAllBytes(new File(filename).toPath());
        String pem = new String(keyBytes);

        if (pem.contains("-----BEGIN PRIVATE KEY-----")) {
            pem = pem.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\r\n", "")
                    .replaceAll("\\s", "").trim();

            keyBytes = Base64.getDecoder().decode(pem);
        }

        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(spec);
    }

    public static X509Certificate loadCertificate(String filename) throws Exception {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        try (InputStream in = new FileInputStream(filename)) {
            return (X509Certificate) cf.generateCertificate(in);
        }
    }

    public static String transformToString(Document doc) throws Exception {
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
        trans.setOutputProperty(OutputKeys.INDENT, "no");
        trans.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "0");
        trans.setOutputProperty(OutputKeys.ENCODING, "ISO-8859-1");

        StringWriter writer = new StringWriter();
        trans.transform(new DOMSource(doc), new StreamResult(writer));
        String signedXml = writer.toString();
//        signedXml = signedXml;

        return signedXml;
//        return writer.toString();
    }

    private static Reference getReference(XMLSignatureFactory fac, String type, String uri, String id) throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {

        return switch (type) {
            case "DOC" -> {

                Reference docRef = fac.newReference(
                        uri+id + "#_"+id,
                        fac.newDigestMethod(DigestMethod.SHA256, null),
                        Arrays.asList(
                                fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
                                ),
                        null,
                        null
                );
                yield docRef;
            }
            case "KEYINFO" -> {

                // Create references with explicit parameter specs
                Reference keyInfoRef = fac.newReference(
                        uri+ id +"#" + id,
                        fac.newDigestMethod(DigestMethod.SHA256, null),
//                        Arrays.asList(
//                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
//                                ),
                        Collections.singletonList(
                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
                        ),
                        null,
                        null
                );
                yield keyInfoRef;
            }
            case "HEADER" -> {
                Reference appHdrRef = fac.newReference(
                        uri,
                        fac.newDigestMethod(DigestMethod.SHA256, null),
                        Arrays.asList(
                                fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
                                ),
                        null,
                        null
                );
                yield appHdrRef;
            }
            default -> null;
        };
    }

    private static KeyInfo getKeyInfo(XMLSignatureFactory fac, X509Certificate cert, String keyInfoId) throws KeyException {
        // Create KeyInfo with RSAKeyValue and X509Certificate
        KeyInfoFactory kif = fac.getKeyInfoFactory();

        // Create RSAKeyValue
        KeyValue kv = kif.newKeyValue(cert.getPublicKey());

        // Create X509Data
        X509Data xd = kif.newX509Data(Collections.singletonList(cert));

        // Create KeyInfo with ID
        KeyInfo ki = kif.newKeyInfo(Arrays.asList(kv,xd), keyInfoId);
        return ki;
    }
}