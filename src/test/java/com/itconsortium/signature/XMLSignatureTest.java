package com.itconsortium.signature;

import com.itconsortium.signature.controller.XMLReferenceController;
import com.itconsortium.signature.utils.ApplicationProperties;
import com.itconsortium.signature.utils.XMLDataCalculatedDigestValue;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.UUID;

@SpringBootTest
public class XMLSignatureTest {

    @Test
    public void testXMLSignatureGeneration() throws Exception {
        // Setup application properties
        ApplicationProperties applicationProperties = new ApplicationProperties();
        applicationProperties.setDocURI("http://localhost:8081/xml/doc/accountLookup/");
        applicationProperties.setKeyInfoURI("http://localhost:8081/xml/keyinfo/");
        applicationProperties.setHeaderRefURI("http://localhost:8081/xml/header");

        // Generate a test keyInfoId
        String keyInfoId = UUID.randomUUID().toString();
        
        // Get the XML message
        String xmlString = XMLReferenceController.getXMLMessage(keyInfoId).strip();

        // Print the original XML for debugging
        System.out.println("Original XML:");
        System.out.println(xmlString);

        // Sign the XML
        XMLDataCalculatedDigestValue signer = new XMLDataCalculatedDigestValue();
        String signedXML = signer.sign(xmlString, "AppHdr", keyInfoId, applicationProperties);
        
        // Write the result to a file for inspection
        try (java.io.FileWriter writer = new java.io.FileWriter("test-output.xml")) {
            writer.write("Original XML:\n");
            writer.write(xmlString);
            writer.write("\n\nGenerated XML:\n");
            writer.write(signedXML);
        }
        
        // Basic validation - check that signature elements are present
        assert signedXML.contains("<ds:Signature");
        assert signedXML.contains("<ds:SignedInfo>");
        assert signedXML.contains("<ds:Reference");
        assert signedXML.contains("<ds:KeyInfo");
        assert signedXML.contains("Id=\"doc-id\"");
    }
}
