package com.itconsortium.signature.model;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreditCustomerRequest {
    private String refNo;
    private String msisdn;
    private String amount;
    private String opId;
    private String opCode;
    private String securityCredential;
    private String thirdPartyId;
    private String password;
    private String username;
    private String sourceIp;


    public TransactionLog createTransactionLog() {
        TransactionLog transactionLog = new TransactionLog();
        transactionLog.setReferenceNumber(this.refNo);
        transactionLog.setMsisdn(this.msisdn);
        transactionLog.setAmount(new BigDecimal(this.amount).setScale(2, BigDecimal.ROUND_DOWN));
        transactionLog.setTransactionStatus(TransStatus.PENDING);
        transactionLog.setTransactionType("CREDIT");
        return transactionLog;
    }

//    public PromptTransactionRequest buildCreditPromptRequest() {
//        PromptTransactionRequest promptTransactionRequest = new PromptTransactionRequest();
//        promptTransactionRequest.setAmount(this.amount);
//        promptTransactionRequest.setMsisdn(this.msisdn);
//        promptTransactionRequest.setTransaction_type(TransactionType.CREDIT);
//        promptTransactionRequest.setReference_number(this.refNo);
//        promptTransactionRequest.setSourceIp(this.sourceIp);
//        return promptTransactionRequest;
//    }
}
