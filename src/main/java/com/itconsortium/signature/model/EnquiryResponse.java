package com.itconsortium.signature.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

@Data
@Component
@EqualsAndHashCode(callSuper = false)
public class EnquiryResponse extends GeneralResponse {
    @JsonInclude(Include.NON_EMPTY)
    private String networkTransactionId;
    @JsonInclude(Include.NON_EMPTY)
    private String balance;
    @JsonInclude(Include.NON_EMPTY)
    private String transactionId;

    public EnquiryResponse() {

    }

    public EnquiryResponse(GeneralResponse generalResponse) {
        this.setResponseCode(generalResponse.getResponseCode());
        this.setResponseMessage(generalResponse.getResponseMessage());
    }
}
