//package com.itconsortiumgh.signature.service;
//
//import com.itconsortiumgh.annotation.annotation.AfterObserving;
//import com.itconsortiumgh.annotation.annotation.BeforeObserving;
//import lombok.NoArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.RandomStringUtils;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//
//@Slf4j
//@Service
//@NoArgsConstructor
//public class BusinessLogic {
//
//    @Autowired
//    CreditServiceRequestBuilder creditServiceRequestBuilder;
//    @Autowired
//    DebitServiceRequestBuilder debitServiceRequestBuilder;
//    @Autowired
//    ApplicationProperties applicationProperties;
//    @Autowired
//    SOAPMessagePoster soapMessagePoster;
//    @Autowired
//    SOAPResponseProcessor soapResponseProcessor;
//    @Autowired
//    SOAPResultProcessor soapResultProcessor;
//    @Autowired
//    NitriteDataAccessService dataAccessService;
//    @Autowired
//    RabbitTemplate rabbitTemplate;
//    @Autowired
//    PromptTransactionService promptTransactionService;
//    @Autowired
//    TransactionCachingService transactionCachingService;
//    private CheckTransactionStatusService checkTransactionStatusService;
//
//    public BusinessLogic(CreditServiceRequestBuilder creditServiceRequestBuilder, DebitServiceRequestBuilder debitServiceRequestBuilder, ApplicationProperties applicationProperties, SOAPMessagePoster soapMessagePoster, SOAPResponseProcessor soapResponseProcessor, SOAPResultProcessor soapResultProcessor, NitriteDataAccessService dataAccessService, RabbitTemplate rabbitTemplate, PromptTransactionService promptTransactionService, CheckTransactionStatusService checkTransactionStatusService) {
//        this.creditServiceRequestBuilder = creditServiceRequestBuilder;
//        this.debitServiceRequestBuilder = debitServiceRequestBuilder;
////        this.applicationProperties = applicationProperties;
//        this.soapMessagePoster = soapMessagePoster;
//        this.soapResponseProcessor = soapResponseProcessor;
//        this.soapResultProcessor = soapResultProcessor;
//        this.dataAccessService = dataAccessService;
//        this.rabbitTemplate = rabbitTemplate;
////        this.promptTransactionService = promptTransactionService;
//        this.checkTransactionStatusService = checkTransactionStatusService;
//        this.transactionCachingService = transactionCachingService;
//    }
//
//
//    public GeneralResponse processCreditCustomerRequest(CreditCustomerRequest creditCustomerRequest) {
//        Mediator mediator = new Mediator();
//        VodafoneResponse creditResponse = new VodafoneResponse();
//        GeneralResponse creditCustomerResponse = new GeneralResponse();
//        TransactionLog transactionLog = creditCustomerRequest.createTransactionLog();
//
//        TransactionLog savedTransaction = null;
//        if (!applicationProperties.isTestFlag()) {
//            savedTransaction = dataAccessService.logTransaction(transactionLog);
//        }
//
//        mediator.setCallerType(applicationProperties.getCallerType());
//        if (creditCustomerRequest.getThirdPartyId() == null) {
//            if (creditCustomerRequest.getUsername().equalsIgnoreCase("itc")) {
//                mediator.setThirdPartyID("ITCONSORTIUM");
//            } else {
//                mediator.setThirdPartyID(creditCustomerRequest.getUsername());
//            }
//        } else {
//            mediator.setThirdPartyID(creditCustomerRequest.getThirdPartyId());
//        }
//
//        if (creditCustomerRequest.getPassword() == null) {
//            mediator.setPassword("dovawWGRQ0w=");
//        } else {
//            if ("itc".equalsIgnoreCase(creditCustomerRequest.getUsername())) {
//                mediator.setPassword("dovawWGRQ0w=");
//            } else {
//                mediator.setPassword(creditCustomerRequest.getPassword());
//            }
//        }
//
//        mediator.setResultUrl(applicationProperties.getCallbackUrl());
//        mediator.setIIdentifierType(applicationProperties.getIIdentifierType());
//
//        if (creditCustomerRequest.getSecurityCredential() == null) {
////            mediator.setSecurityCredential("hc/Am+Wi9Cs=");
//            mediator.setSecurityCredential(applicationProperties.getSecurityCredential());
//        } else {
//            mediator.setSecurityCredential(creditCustomerRequest.getSecurityCredential());
//        }
//
//        mediator.setIIdentifier(Optional.ofNullable(creditCustomerRequest.getOpId()).orElse(applicationProperties.getOpId()));
//        mediator.setShortCode(Optional.ofNullable(creditCustomerRequest.getOpCode()).orElse(applicationProperties.getOpCode()));
//
//        mediator.setRIdentifierType(applicationProperties.getCRIdentifierType());
//        mediator.setRIdentifier(creditCustomerRequest.getMsisdn());
//        mediator.setCommandId(applicationProperties.getCredCustCommandId());
//
//        Parameters parameters = new Parameters();
//        List<Parameter> tmpParameterList = new ArrayList<>();
//
//        Parameter parameter1 = new Parameter();
//        parameter1.setKey(applicationProperties.getAmount());
//        parameter1.setValue(creditCustomerRequest.getAmount());
//        tmpParameterList.add(parameter1);
//
//        Parameter parameter2 = new Parameter();
//        parameter2.setKey(applicationProperties.getCurrency());
//        parameter2.setValue(applicationProperties.getCurrencyVal());
//        tmpParameterList.add(parameter2);
//
//        parameters.setParameterList(tmpParameterList);
//
//        ReferenceData referenceData = new ReferenceData();
//        List<ReferenceItem> tmpReferenceItemList = new ArrayList<>();
//
//        ReferenceItem referenceItem = new ReferenceItem();
//        referenceItem.setKey(applicationProperties.getPosDeviceId());
////        referenceItem.setValue(applicationProperties.getPosDeviceIdVal());
//        referenceItem.setValue(creditCustomerRequest.getRefNo());
//        tmpReferenceItemList.add(referenceItem);
//        referenceData.setReferenceItemList(tmpReferenceItemList);
//
//        try {
//            String requestXml = creditServiceRequestBuilder.request(mediator, parameters, referenceData);
//            String headerXml = creditServiceRequestBuilder.header(requestXml);
//            log.info("========== Credit Request XML {}", XMLFormatter.format(headerXml));
//            PromptTransactionRequest promptRequest = creditCustomerRequest.buildCreditPromptRequest();
//            if (!applicationProperties.isTestFlag()) {
//                transactionCachingService.cacheTransaction(promptRequest);
//            }
//            if (applicationProperties.isTestFlag()) {
//                creditResponse.setResponseCode(ResponseCode.VODAFONE_SUCCESS);
//                creditResponse.setResponseMessage(ResponseMessage.PROCESSING_PAYMENT);
//                String conversationId = RandomStringUtils.random(15, true, true);
//                creditResponse.setConversationID(conversationId);
//                if (!applicationProperties.isTestFlag()) {
//                    savedTransaction.setConversationId(conversationId);
//                    savedTransaction.setTransactionStatus(TransStatus.CALLBACK);
//                    savedTransaction.setTransactionId(RandomStringUtils.random(15, false, true));
//                    savedTransaction.setResultCode(ResponseCode.SUCCESS);
//                    savedTransaction.setResultDescription(ResponseMessage.PAYMENT_SUCCESS);
//                }
//                rabbitTemplate.convertAndSend(applicationProperties.getVodafoneCashExchange(), applicationProperties.getUniwalletCallbackQueue(), transactionLog);
//                log.info("========= Successfully Sent Request to Uniwallet Callback Queue =========");
//            } else {
//
//                log.info("========== Hitting {} for a response ==========", applicationProperties.getUrl());
//                String responseXml = soapMessagePoster.sendMessage(applicationProperties.getUrl(), headerXml);
//                log.info("========== Credit Response XML {}", XMLFormatter.format(responseXml));
//                int indexOfResponseStart = responseXml.indexOf("<Response>");
//                int indexOfResponseEnd = responseXml.indexOf("</Response>");
//                String responseStr = responseXml.substring(indexOfResponseStart, indexOfResponseEnd);
//                creditResponse = soapResponseProcessor.processVodafoneResponse(responseStr + "</Response>");
//            }
//            log.info("========== Vodafone Response {}", JsonUtility.toJson(creditResponse));
//            if (ResponseCode.VODAFONE_SUCCESS.equalsIgnoreCase(creditResponse.getResponseCode())) {
//                creditCustomerResponse.setResponseCode(ResponseCode.PROCESSING_PAYMENT);
//                creditCustomerResponse.setResponseMessage(ResponseMessage.PROCESSING_PAYMENT);
//                transactionLog.setConversationId(creditResponse.getConversationID());
//
//                if (!applicationProperties.isTestFlag()) {
//                    dataAccessService.logTransactionStatus(transactionLog);
//                }
//            } else {
//                creditCustomerResponse.setResponseCode(ResponseCode.GENERAL_FAILURE);
//                creditCustomerResponse.setResponseMessage(creditResponse.getResponseMessage());
//                if (!applicationProperties.isTestFlag()) {
//                    savedTransaction.setResultCode(creditResponse.getResponseCode());
//                    savedTransaction.setResultDescription(creditResponse.getResponseMessage());
//                    savedTransaction.setTransactionStatus(TransStatus.COMPLETED);
//                    dataAccessService.logTransactionStatus(savedTransaction);
//                }
//            }
//        } catch (Exception e) {
//            log.error("", e);
//            creditCustomerResponse.setResponseCode(ResponseCode.GENERAL_FAILURE);
//            creditCustomerResponse.setResponseMessage(ResponseMessage.PAYMENT_FAILED);
//            if (!applicationProperties.isTestFlag()) {
//                savedTransaction.setResultCode(ResponseCode.GENERAL_FAILURE);
//                savedTransaction.setResultDescription(ResponseMessage.GENERAL_FAILURE + e.getMessage());
//                savedTransaction.setTransactionStatus(TransStatus.COMPLETED);
//                dataAccessService.logTransactionStatus(transactionLog);
//            }
//        }
//        return creditCustomerResponse;
//    }
//
//    @BeforeObserving(topic = "processDebitCustomerRequest", key = "msisdn")
//    public GeneralResponse processDebitCustomerRequest(DebitCustomerRequest debitCustomerRequest) {
//        Mediator mediator = new Mediator();
//        GeneralResponse debitCustomerResponse = new GeneralResponse();
//        VodafoneResponse debitResponse = new VodafoneResponse();
//        TransactionLog transactionLog = debitCustomerRequest.createTransactionLog();
//        TransactionLog savedTransaction = null;
//        if (!applicationProperties.isTestFlag()) {
//            savedTransaction = dataAccessService.logTransaction(transactionLog);
//        }
//        String inMsisdn = debitCustomerRequest.getMsisdn();
//        String outMsisdn = "";
//        if (inMsisdn.length() == 12) {
//            outMsisdn = inMsisdn.substring(8, 12);
//        } else if (inMsisdn.length() == 10) {
//            outMsisdn = inMsisdn.substring(6, 10);
//        } else if (inMsisdn.length() == 13) {
//            outMsisdn = inMsisdn.substring(9, 13);
//        }
//        log.info("========== The full Msisdn: {}", inMsisdn);
//        log.info("========== The truncated Msisdn: {}", outMsisdn);
//
//        mediator.setCallerType(applicationProperties.getCallerType());
//        mediator.setThirdPartyID(debitCustomerRequest.getThirdPartyId());
//        mediator.setPassword(debitCustomerRequest.getPassword());
//        mediator.setResultUrl(applicationProperties.getCallbackUrl());
//        mediator.setIIdentifierType(applicationProperties.getIIdentifierType());
//
//        mediator.setSecurityCredential(Optional.ofNullable(debitCustomerRequest.getSecurityCredential()).orElse(applicationProperties.getSecurityCredential()));
//        mediator.setIIdentifier(Optional.ofNullable(debitCustomerRequest.getOpId()).orElse(applicationProperties.getOpId()));
//        mediator.setShortCode(Optional.ofNullable(debitCustomerRequest.getOpCode()).orElse(applicationProperties.getOpCode()));
//
//        mediator.setRIdentifierType(applicationProperties.getDRIdentifierType());
//        mediator.setRIdentifier(debitCustomerRequest.getVoucher());
//        mediator.setCommandId(applicationProperties.getDebtCustCommandId());
//        mediator.setRemark(debitCustomerRequest.getRefNo());
//
//        Parameters parameters = new Parameters();
//        List<Parameter> tmpParameterList = new ArrayList<>();
//
//        Parameter parameter1 = new Parameter();
//        parameter1.setKey(applicationProperties.getVoucher());
//        parameter1.setValue(outMsisdn);
//        tmpParameterList.add(parameter1);
//
//        Parameter parameter2 = new Parameter();
//        parameter2.setKey(applicationProperties.getCurrency());
//        parameter2.setValue(applicationProperties.getCurrencyVal());
//        tmpParameterList.add(parameter2);
//
//        Parameter parameter3 = new Parameter();
//        parameter3.setKey(applicationProperties.getAmount());
//        parameter3.setValue(debitCustomerRequest.getAmount());
//        tmpParameterList.add(parameter3);
//        parameters.setParameterList(tmpParameterList);
//
//        try {
//            String requestXml = debitServiceRequestBuilder.request(mediator, parameters);
//            String headerXml = debitServiceRequestBuilder.header(requestXml);
//            log.info("========== Debit Request XML {}", XMLFormatter.format(headerXml));
//            if (applicationProperties.isTestFlag()) {
//                String conversationId = RandomStringUtils.random(15, true, true);
//
//                debitResponse.setResponseCode(ResponseCode.VODAFONE_SUCCESS);
//                debitResponse.setResponseMessage(ResponseMessage.PROCESSING_PAYMENT);
//                debitResponse.setConversationID(conversationId);
//                MobileMoneyCallback mobileMoneyCallback = new MobileMoneyCallback();
//                mobileMoneyCallback.setRefNo(debitCustomerRequest.getRefNo());
//                mobileMoneyCallback.setNetworkTransactionId(RandomStringUtils.random(15, false, true));
//                mobileMoneyCallback.setResponseCode(ResponseCode.SUCCESS);
//                mobileMoneyCallback.setResponseMessage(ResponseMessage.PAYMENT_SUCCESS);
//
//                rabbitTemplate.convertAndSend(applicationProperties.getVodafoneCashExchange(), applicationProperties.getUniwalletCallbackQueue(), mobileMoneyCallback);
//                log.info("========= Successfully Sent Request to Uniwallet Callback Queue =========");
//            } else {
//                log.info("========== Hitting {} for a response ==========", applicationProperties.getUrl());
//
//                if (!applicationProperties.isTestFlag()) {
//                    String responseXml = soapMessagePoster.sendMessage(applicationProperties.getUrl(), headerXml);
//                    log.info("========== Debit Response XML {}", XMLFormatter.format(responseXml));
//                    int indexOfResponseStart = responseXml.indexOf("<Response>");
//                    int indexOfResponseEnd = responseXml.indexOf("</Response>");
//                    String responseStr = responseXml.substring(indexOfResponseStart, indexOfResponseEnd);
//                    debitResponse = soapResponseProcessor.processVodafoneResponse(responseStr + "</Response>");
//                }
//            }
//            log.info("========== Vodafone Response {}", JsonUtility.toJson(debitResponse));
//            if (ResponseCode.VODAFONE_SUCCESS.equalsIgnoreCase(debitResponse.getResponseCode())) {
//                debitCustomerResponse = successfulResponse(debitCustomerResponse, debitResponse, transactionLog, savedTransaction);
//            } else {
//                debitCustomerResponse = failedResponse(debitCustomerResponse, debitResponse, transactionLog, savedTransaction);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            failedExceptionResponse(debitCustomerResponse, transactionLog, savedTransaction, e);
//        }
//        return debitCustomerResponse;
//    }
//
//    @AfterObserving(key = "msisdn", topic = "processDebitCustomerRequest", isSuccessful = false)
//    private GeneralResponse failedExceptionResponse(GeneralResponse debitCustomerResponse, TransactionLog transactionLog, TransactionLog savedTransaction, Exception e) {
//        debitCustomerResponse.setResponseCode(ResponseCode.GENERAL_FAILURE);
//        debitCustomerResponse.setResponseMessage(ResponseMessage.PAYMENT_FAILED);
//        if (!applicationProperties.isTestFlag()) {
//            savedTransaction.setResultCode(ResponseCode.GENERAL_FAILURE);
//            savedTransaction.setResultDescription(ResponseMessage.GENERAL_FAILURE + e.getMessage());
//            savedTransaction.setTransactionStatus(TransStatus.COMPLETED);
//            dataAccessService.logTransactionStatus(transactionLog);
//        }
//        return debitCustomerResponse;
//    }
//
//    @AfterObserving(key = "msisdn", topic = "processDebitCustomerRequest", isSuccessful = false)
//    private GeneralResponse failedResponse(GeneralResponse debitCustomerResponse, VodafoneResponse debitResponse, TransactionLog transactionLog, TransactionLog savedTransaction) {
//        debitCustomerResponse.setResponseCode(debitResponse.getResponseCode());
//        debitCustomerResponse.setResponseMessage(debitResponse.getResponseMessage());
//        if (!applicationProperties.isTestFlag()) {
//            savedTransaction.setResultCode(debitResponse.getResponseCode());
//            savedTransaction.setResultDescription(debitCustomerResponse.getResponseMessage());
//            savedTransaction.setTransactionStatus(TransStatus.COMPLETED);
//            dataAccessService.logTransactionStatus(transactionLog);
//        }
//
//        return debitCustomerResponse;
//    }
//
//    @AfterObserving(key = "msisdn", topic = "processDebitCustomerRequest", isSuccessful = true)
//    private GeneralResponse successfulResponse(GeneralResponse debitCustomerResponse, VodafoneResponse debitResponse, TransactionLog transactionLog, TransactionLog savedTransaction) {
//        debitCustomerResponse.setResponseCode(ResponseCode.PROCESSING_PAYMENT);
//        debitCustomerResponse.setResponseMessage(ResponseMessage.PROCESSING_PAYMENT);
//        if (!applicationProperties.isTestFlag()) {
//            savedTransaction.setConversationId(debitResponse.getConversationID());
//            dataAccessService.logTransactionStatus(transactionLog);
//        }
//        return debitCustomerResponse;
//    }
//
//    @BeforeObserving(topic = "initiateDebitPromptRequest", key = "msisdn")
//    public GeneralResponse initiateDebitPromptRequest(DebitCustomerRequest debitCustomerRequest) {
//        GeneralResponse generalResponse = new GeneralResponse();
//        PromptTransactionRequest promptRequest = debitCustomerRequest.buildPromptTransactionRequest();
//        log.info("==== DEBIT PROMPT REQUEST {}", promptRequest);
//        return getTransactionResponse(generalResponse, promptRequest);
//
//    }
//
//    private GeneralResponse getTransactionResponse(GeneralResponse generalResponse, PromptTransactionRequest promptRequest) {
//        transactionCachingService.cacheTransaction(promptRequest);
//
//        PromptTransactionResponse debitResponse = promptTransactionService.postTransactionRequest(promptRequest);
//        if (!TransStatus.CREATED.equalsIgnoreCase(debitResponse.getStatus())) {
//            generalResponse = promptFailureResponse(generalResponse);
//        } else {
//            generalResponse = promptSuccessfulResponse(generalResponse);
//        }
//        return generalResponse;
//    }
//
//    @AfterObserving(key = "msisdn", topic = "initiateDebitPromptRequest", isSuccessful = false)
//    private GeneralResponse promptFailureResponse(GeneralResponse generalResponse) {
//        generalResponse.setResponseCode(ResponseCode.GENERAL_FAILURE);
//        generalResponse.setResponseMessage(ResponseMessage.GENERAL_FAILURE);
//        return generalResponse;
//    }
//
//    @AfterObserving(key = "msisdn", topic = "initiateDebitPromptRequest", isSuccessful = true)
//    private GeneralResponse promptSuccessfulResponse(GeneralResponse generalResponse) {
//        generalResponse.setResponseCode(ResponseCode.PROCESSING_PAYMENT);
//        generalResponse.setResponseMessage(ResponseMessage.PROCESSING_PAYMENT);
//        return generalResponse;
//    }
//
//    public GeneralResponse initiateCreditPromptRequest(CreditCustomerRequest creditCustomerRequest) {
//        GeneralResponse generalResponse = new GeneralResponse();
//        PromptTransactionRequest promptRequest = creditCustomerRequest.buildCreditPromptRequest();
//        log.info("==== CREDIT PROMPT REQUEST {}", promptRequest);
//        return getTransactionResponse(generalResponse, promptRequest);
//
//    }
//
//    public EnquiryResponse checkTransactionStatus(TransactionEnquiryRequest transactionEnquiryRequest) {
//        EnquiryResponse enquiryResponse;
//        GetTransactionStatusResponse response =
//                checkTransactionStatusService.checkTransactionStatus(transactionEnquiryRequest.getTransactionId());
//        if (TransStatus.SUCCESS.equalsIgnoreCase(response.getStatus())) {
//            enquiryResponse = new EnquiryResponse();
//            enquiryResponse.setResponseCode(ResponseCode.SUCCESS);
//            enquiryResponse.setResponseMessage(ResponseMessage.PAYMENT_SUCCESS);
//            enquiryResponse.setNetworkTransactionId(response.getNetworkTransactionId());
//        } else {
//            TransactionResponseDetail responseDetails = TransactionResponseDetailFactory
//                    .getResponse(response.getStatus())
//                    .orElseThrow(() -> new IllegalArgumentException("Invalid Operator"));
//
//            GeneralResponse generalResponse = responseDetails.getTransactionResponse();
//            enquiryResponse = new EnquiryResponse(generalResponse);
//
//        }
//        enquiryResponse.setTransactionId(transactionEnquiryRequest.getTransactionId());
//        return enquiryResponse;
//    }
//
//    public VodafoneCallbackResponse processXMLCallback(String requestXml) {
//        VodafoneCallbackResponse callbackResponse = new VodafoneCallbackResponse();
//        VodafoneCallback vodafoneCallback = soapResultProcessor.processResult(requestXml);
//        log.info("========== Vodafone Callback {} ", JsonUtility.toJson(vodafoneCallback));
//
//        if (ResponseCode.VODAFONE_SUCCESS.equalsIgnoreCase(vodafoneCallback.getResultCode())) {
//            callbackResponse.setResponseCode(ResponseCode.SUCCESS);
//            callbackResponse.setResponseMessage(ResponseMessage.PAYMENT_SUCCESS);
//        } else {
//            callbackResponse.setResponseCode(vodafoneCallback.getResultCode());
//            callbackResponse.setResponseMessage(vodafoneCallback.getResultDesc());
//        }
//        callbackResponse.setRefNo(callbackResponse.getRefNo());
//        rabbitTemplate.convertAndSend(applicationProperties.getVodafoneCashExchange(), applicationProperties.getVodafoneCallbackQueue(), requestXml);
//        log.info("========= Successfully Sent Request to Vodafone Callback Queue =========");
//
//        return callbackResponse;
//    }
//
//    public void processDebitPromptCallback(PromptTransactionResponse callback) {
//        log.info("====== INCOMING CALLBACK {}", callback);
//        rabbitTemplate.convertAndSend(applicationProperties.getVodafoneCashExchange(),
//                applicationProperties.getVodafonePromptCallbackQueue(), callback);
//        log.info("========= Successfully Sent Request to Vodafone Prompt Callback Queue =========");
//    }
//}
