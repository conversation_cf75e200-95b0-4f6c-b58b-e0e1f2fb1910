package com.itconsortium.signature.controller;//package com.itconsortium.signature.controller;

import com.itconsortium.signature.utils.ApplicationProperties;
import com.itconsortium.signature.utils.XMLDataCalculatedDigestValue;
import com.itconsortium.signature.utils.XMLFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.time.Instant;
import java.util.UUID;

import static com.itconsortium.signature.utils.XMLDataCalculatedDigestValue.*;


@Slf4j
@RestController
@RequestMapping("/xml")
public class XMLReferenceController {

    @Autowired
    ApplicationProperties applicationProperties;

    @GetMapping(value = "doc/accountLookup/{refId}", produces = MediaType.TEXT_XML_VALUE)
    public String lookupRef(@PathVariable(name = "refId") String refId) {
        return XMLFormatter.format(getXMLMessage(refId));
    }

    @GetMapping(value = "doc/creditRequest/{refId}", produces = MediaType.TEXT_XML_VALUE)
    public String creditRef(@PathVariable(name = "refId") String refId) {
        return "";
    }

    @GetMapping(value = "doc/checkTransactionStatus/{refId}", produces = MediaType.TEXT_XML_VALUE)
    public String checkTransactionStatus(@PathVariable(name = "refId") String refId) {
        return "";
    }

    @GetMapping(value = "keyinfo/{refId}", produces = MediaType.TEXT_XML_VALUE)
    public String keyRef(@PathVariable(name = "refId") String refId) {
        return XMLFormatter.format(getKeyInfoXMLMessage(refId));
    }

    @GetMapping(value = "header", produces = MediaType.TEXT_XML_VALUE)
    public String header() {
        return XMLFormatter.format(getHeaderXMLMessage());
    }

    private static String getHeaderXMLMessage() {
        return """
                <AppHdr xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01"></AppHdr>
                """;
    }

    @GetMapping(value = "test-service", produces = MediaType.TEXT_XML_VALUE)
    private String testXMLService() throws Exception {
        PrivateKey privateKey = loadPrivateKey("src/main/resources/private_key.key");
        X509Certificate cert = loadCertificate("src/main/resources/rswitch.etransflow.io.crt");

        // References which xml element within the xml data to sign
        String keyInfoId = UUID.randomUUID().toString();
//        keyInfoId = "dc1162cc-81ec-4b93-983e-153c448668ed";
        String xmlString = getXMLMessage(keyInfoId).strip();
        String responseXML = new XMLDataCalculatedDigestValue().sign(xmlString, "AppHdr", keyInfoId, applicationProperties);

        return responseXML;
    }

    private static String getKeyInfoXMLMessage(String keyInfo) {
        String xml = String.format("""
                                <KeyInfo>
                                <KeyValue>
                                <RSAKeyValue>
                                <Modulus>rmlgFkrxitmvMfl9aDEI2l8KyolOEUXx/Peje3ncq4RWjxGT6bwEHliimIBx9Y1k+o0zwGlhYUD7wM6lScohdopKqGF5wIFiE3HJeL6S5OK5OWxEuAe7Aed0TLIgOidPmnaaLa0MHzl1VEsw8+Dlw6GScvEdrlhjzRq0SWfi5+tuLb1OjyoxvI6LaZQChKRg27dhDF2nW7oHOLVZPe/1KCuvMPO8DsrFdakLK+LsgTkHv5hUEUQFxyskgnreev9FDfvJZwI2wxjWjdpXHRyiW3BH5BYKQ/gGe2fFiFH6AOFrx3JjsYiLSdVA6+0Ky0HC4YiDVN0dbOGW1ouxkuw5jQ==</Modulus>
                                <Exponent>AQAB</Exponent>
                                </RSAKeyValue>
                                </KeyValue>
                                <X509Data>
                                <X509Certificate>MIIEETCCAvmgAwIBAgICALswDQYJKoZIhvcNAQELBQAwgYwxCzAJBgNVBAYTAlJXMQ8wDQYDVQQI
                                EwZLaWdhbGkxCzAJBgNVBAcTAlJXMRAwDgYDVQQKEwdSU3dpdGNoMQ8wDQYDVQQLEwZSLU5EUFMx
                                FjAUBgNVBAMTDVItTkRQUyBTVUIgQ0ExJDAiBgkqhkiG9w0BCQEWFWluZm9zZWNAcnN3aXRjaC5j
                                by5ydzAeFw0yNTA2MTkwNzM4MDBaFw0yNjA2MTkwNzM4MDBaMIGqMQswCQYDVQQGEwJHSDEWMBQG
                                A1UECAwNR3JlYXRlciBBY2NyYTEOMAwGA1UEBwwFQWNjcmExFjAUBgNVBAoMDUlUIENvbnNvcnRp
                                dW0xEDAOBgNVBAsMB0luZm9TZWMxHjAcBgNVBAMMFXJzd2l0Y2guZXRyYW5zZmxvdy5pbzEpMCcG
                                CSqGSIb3DQEJARYaaW5mb3NlY0BpdGNvbnNvcnRpdW1naC5jb20wggEiMA0GCSqGSIb3DQEBAQUA
                                A4IBDwAwggEKAoIBAQCuaWAWSvGK2a8x+X1oMQjaXwrKiU4RRfH896N7edyrhFaPEZPpvAQeWKKY
                                gHH1jWT6jTPAaWFhQPvAzqVJyiF2ikqoYXnAgWITccl4vpLk4rk5bES4B7sB53RMsiA6J0+adpot
                                rQwfOXVUSzDz4OXDoZJy8R2uWGPNGrRJZ+Ln624tvU6PKjG8jotplAKEpGDbt2EMXadbugc4tVk9
                                7/UoK68w87wOysV1qQsr4uyBOQe/mFQRRAXHKySCet56/0UN+8lnAjbDGNaN2lcdHKJbcEfkFgpD
                                +AZ7Z8WIUfoA4WvHcmOxiItJ1UDr7QrLQcLhiINU3R1s4ZbWi7GS7DmNAgMBAAGjXTBbMAkGA1Ud
                                EwQCMAAwHQYDVR0OBBYEFJuoPdrs2GGcpO5jVRnoImbOm+8OMB8GA1UdIwQYMBaAFBA/2uMlpd8N
                                Un2CZlXSrD6H/sMiMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsFAAOCAQEAPwkIgilusBot
                                qduNYjpBxsqEO0QYqIJMcmEA6oncehAC3PB9mQbICq6B/0OiXJUm4u/J10FgtP3M9h1Fz3gUk1Lw
                                mFWnpx9ITkSYgux5W5jGNnzsq7VAyaMFlv+M0KaM6IuSS/y/opJKEcG15FfdoXQBCJcKzElvacXg
                                /hjBEO9oYkjo2eXU3zQSEj4v01cwE9d7ZVlHWAc4z+sgp2MzkwxZPeCImXtzbjRCS2im1Sr2n1lf
                                904R+ZqdPu8zpL5EqpaLQTN6b1KJModcp6clplt2fYV/nR3iNeHujWqhsLZUeHawdjwlXdMGv0tU
                                dwMK/MWshuzBj4UJMLyHeEiZiA==</X509Certificate>
                                </X509Data>
                                </KeyInfo>
        """.replaceAll("/r/n", ""));

        return xml;
    }

    public static String getXMLMessage(String keyInfoId) {
//        String keyInfoId = UUID.randomUUID().toString();
        String msgId = "CBAFRWRW" + System.currentTimeMillis();
        String creDtTm = Instant.now().toString();

        msgId = "CBAFRWRW1753711044158";
        creDtTm = "2025-07-28T17:18:47+02:00";

        String xml = String.format("""
<BusinessMessage>
%s
<Document Id="doc-id" xmlns="urn:iso:std:iso:20022:tech:xsd:camt.003.001.07"><GetAcct><MsgHdr><MsgId>%s</MsgId><CreDtTm>%s</CreDtTm></MsgHdr><AcctQryDef><AcctCrit><NewCrit><SchCrit><AcctId><EQ><Othr><Id>***********</Id></Othr></EQ></AcctId></SchCrit></NewCrit></AcctCrit></AcctQryDef></GetAcct></Document>
</BusinessMessage>
""".trim(), getHeaderXMLMessage(), msgId, creDtTm);

//        return "<BusinessMessage><AppHdr xmlns=\"urn:iso:std:iso:20022:tech:xsd:head.001.001.01\"></AppHdr></BusinessMessage>\n";
        return xml.replaceAll("/r/n", "");
    }
}
