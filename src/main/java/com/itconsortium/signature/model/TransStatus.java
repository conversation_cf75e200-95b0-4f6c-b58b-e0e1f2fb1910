package com.itconsortium.signature.model;

import lombok.Data;
import org.springframework.stereotype.Component;

@Data
@Component
public class TransStatus {

    public static final String CREATED = "CREATED";
    public static final String PENDING = "Pending";
    public static final String UPDATED = "Updated";
    public static final String CALLBACK = "Callback";
    public static final String COMPLETED = "Completed";
    public static final String SUCCESS = "SUCCESS";
    public static final String FAIL = "FAILED";
    public static final String TIMEOUT = "TIMEOUT";

}
