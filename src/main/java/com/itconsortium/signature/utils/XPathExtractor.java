package com.itconsortium.signature.utils;

import com.amazonaws.util.StringInputStream;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.OutputKeys;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

public class XPathExtractor {

    public static void main(String[] args) {

        String xmlString = "<root><data><item id=\"1\">Value 1</item><item id=\"2\">Value 2</item></data></root>";
        String xpathExpression = "/root/data/item[@id='1']"; // XPath to extract a specific item

        extractXML(xmlString, xpathExpression);
    }

    public static String extractXML(String xmlString, String xpathExpression) {
        String extractedXml = null;
        try {
            // 1. Load the XML Document
            Document doc = loadXMLFrmStringToDocument(xmlString);

            // 2. Prepare the XPath Expression
            XPathFactory xpathFactory = XPathFactory.newInstance();
            XPath xpath = xpathFactory.newXPath();
            XPathExpression expr = xpath.compile(xpathExpression);

            // 3. Evaluate the XPath Expression
            Node selectedNode = (Node) expr.evaluate(doc, XPathConstants.NODE);
            extractedXml = "";
            if (selectedNode != null) {
                // 4. Convert the Extracted Node to a String
                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                Transformer transformer = transformerFactory.newTransformer();
                transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes"); // Optional: omit XML declaration

                StringWriter writer = new StringWriter();
                transformer.transform(new DOMSource(selectedNode), new StreamResult(writer));
                extractedXml = writer.toString().replaceAll("(?m)^\\s*$\\r?\\n", "");

//                System.out.println("Extracted XML as String:\n" + extractedXml);
            } else {
                System.out.println("No node found for the given XPath expression.");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return extractedXml;
    }

    public static Document loadXMLFrmStringToDocument(String xmlString) throws ParserConfigurationException, SAXException, IOException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new StringInputStream(xmlString));
        return doc;
    }
}