//package com.itconsortium.signature.controller;
//
//import com.itconsortium.signature.model.*;
//import com.itconsortiumgh.annotation.annotation.BeforeObserving;
//import com.itconsortiumgh.annotation.model.JsonUtility;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//
//@Slf4j
//@RestController
//@RequestMapping("/rswitch")
//public class TransactionController {
////    @Autowired
////    BusinessLogic businessLogic;
//
//    @PostMapping("debit/request-to-pay")
//    @BeforeObserving(topic = "prepare", key = "msisdn")
//    public GeneralResponse requestToPay(@RequestBody @Validated DebitCustomerRequest debitCustomerRequest,
//                                                HttpServletRequest httpServletRequest) {
//        log.info("========= Incoming Debit Customer Request: {}", JsonUtility.toJson(debitCustomerRequest));
//        GeneralResponse debitCustomerResponse;
//        debitCustomerRequest.setSourceIp(httpServletRequest.getRemoteAddr());
////        debitCustomerResponse = businessLogic.initiateDebitPromptRequest(debitCustomerRequest);
//        log.info("========== Debit Customer Response: {}", JsonUtility.toJson(debitCustomerResponse));
//        return debitCustomerResponse;
//    }
//
//    @PostMapping("credit/customer")
//    @BeforeObserving(topic = "prepare", key = "msisdn")
//    public GeneralResponse creditCustomer(@RequestBody @Validated CreditCustomerRequest creditCustomerRequest,
//                                                 HttpServletRequest httpServletRequest) {
//        log.info("========= Incoming Credit Customer Request: {}", JsonUtility.toJson(creditCustomerRequest));
//        GeneralResponse creditCustomerResponse;
//        creditCustomerRequest.setSourceIp(httpServletRequest.getRemoteAddr());
////        creditCustomerResponse = businessLogic.initiateCreditPromptRequest(creditCustomerRequest);
//        log.info("========== Credit Customer Response: {}", JsonUtility.toJson(creditCustomerResponse));
//        return creditCustomerResponse;
//    }
//
//    @PostMapping("check-transaction-status")
//    @BeforeObserving(topic = "prepare", key = "msisdn")
//    public EnquiryResponse checkTransactionStatus(@RequestBody TransactionEnquiryRequest transactionEnquiryRequest) {
//        log.info("===== Transaction Enquiry Request {}", transactionEnquiryRequest);
//        EnquiryResponse enquiryResponse = null;
////        enquiryResponse = businessLogic.checkTransactionStatus(transactionEnquiryRequest);
//        log.info("======= Transaction Enquiry Response");
//        return enquiryResponse;
//    }
//
//}
