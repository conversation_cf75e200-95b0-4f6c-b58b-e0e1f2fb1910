//package com.itconsortium.signature.utils;
//
//import javax.xml.crypto.dsig.*;
//import javax.xml.crypto.dsig.dom.DOMSignContext;
//import javax.xml.crypto.dsig.keyinfo.*;
//import javax.xml.crypto.dsig.spec.*;
//import javax.xml.parsers.*;
//import javax.xml.transform.*;
//import javax.xml.transform.dom.*;
//import javax.xml.transform.stream.*;
//import javax.xml.xpath.*;
//
//import lombok.extern.slf4j.Slf4j;
//import org.w3c.dom.*;
//import org.xml.sax.InputSource;
//import java.io.*;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.security.*;
//import java.security.cert.CertificateFactory;
//import java.security.cert.X509Certificate;
//import java.security.spec.PKCS8EncodedKeySpec;
//import java.time.Instant;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//
//
//public class RNDPSSignatureGenerator {
//
//    public static void main(String[] args) throws Exception {
//
//        // Generate UUID for KeyInfo
////        String keyInfoId = UUID.randomUUID().toString();
////        String msgId = "CBAFRWRW" + System.currentTimeMillis();
////        String creDtTm = Instant.now().toString();
////        msgId = getMessageId();
//        // Create the XML document
//        String headerXml = new RNDPSSignatureGenerator().getHeaderXMLMessage();
//        XMLDataCalculatedDigestValue.sign(headerXml, "AppHdr");
//        String xml = getXMLMessage();
//        XMLDataCalculatedDigestValue.sign(xml, "Document");
//
////        System.out.println("Message "+ msgId);
////         msgId = "CBAFRWRW1753711044158";
////         creDtTm = "2025-07-28T13:57:24.160745Z";
////         keyInfoId = "dc1162cc-81ec-4b93-983e-153c448668ed";
//
//        // Create the XML document
//        String xml = new RNDPSSignatureGenerator().getXMLMessage();
//        String headerXml = new RNDPSSignatureGenerator().getHeaderXMLMessage();
//
//        // Parse the XML
////        Document docHeader = parseXml(xml);
//        Document headerDoc = parseXml(headerXml);
//        Document doc = parseXml(headerXml);
//        Document docBody = parseXml(xml);
////        Element appBody = getAppHdrElement(headerDoc);
////        Element appBodyAppend = getDocElement(docBody);
//
////        appHdr.setIdAttribute("ID", true);
////        appBody.setIdAttribute("ID", true);
////        // Retreive Assertion ID because it is used in the URI attribute of the signature.
//        // Instance main XML Signature Toolkit.
//        // Create signature factory
//        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");
//        XPathFactory xPathfactory = XPathFactory.newInstance();
//        XPath xpath = xPathfactory.newXPath();
//        XPathExpression hdrExprAssertionID = xpath.compile("//*[local-name()='AppHdr']//@ID");
//        String appHdrId = (String) hdrExprAssertionID.evaluate(doc, XPathConstants.STRING);
//        System.out.println("appHdrId "+ appHdrId);
//        XPathExpression docExprAssertionID = xpath.compile("//*[local-name()='Document']//@ID");
//        String docID = (String) docExprAssertionID.evaluate(docBody, XPathConstants.STRING);
////        System.out.println("Document "+ docExprAssertionID.evaluate(docBody, XPathConstants.NODE));
//
////        String appHdrXml = XPathExtractor.extractXML(xml, "//*[local-name()='AppHdr']");
////
////        String documentBodyXml = XPathExtractor.extractXML(xml, "//*[local-name()='Document']");
//
//        // References which xml element to reference
//        Reference keyInfoRef = getReference(fac, "KEYINFO", keyInfoId);
//        Reference appHdrRef = getReference(fac, "HEADER", appHdrId);
//        Reference docRef = getReference(fac, "DOC", docID);
//
//        // Create SignedInfo
//        SignedInfo si = fac.newSignedInfo(
//                fac.newCanonicalizationMethod(CanonicalizationMethod.EXCLUSIVE, (C14NMethodParameterSpec) null),
//                fac.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null),
//                Arrays.asList(keyInfoRef, appHdrRef, docRef)
//        );
//
//        // Create KeyInfo with RSAKeyValue and X509Certificate
//        KeyInfoFactory kif = fac.getKeyInfoFactory();
//
//        // Create RSAKeyValue
//        KeyValue kv = kif.newKeyValue(cert.getPublicKey());
//
//        // Create X509Data
//        X509Data xd = kif.newX509Data(Collections.singletonList(cert));
//
//        // Create KeyInfo with ID
//        KeyInfo ki = kif.newKeyInfo(Arrays.asList(kv, xd), keyInfoId);
//
//        // Sign the document
//        DOMSignContext dsc = new DOMSignContext(privateKey, getAppHdrElement(doc));
//        dsc.setDefaultNamespacePrefix("ds");
//        dsc.putNamespacePrefix("http://www.w3.org/2000/09/xmldsig#", "ds");
//
//        // Create and sign the signature
//        XMLSignature signature = fac.newXMLSignature(si, ki);
//        signature.sign(dsc);
//
//        // Output the signed XML with proper formatting
//        String signedXml = transformToString(doc);
////      String signedXml = doc.getXmlEncoding();
//        System.out.println(signedXml);
//        System.out.println(keyInfoRef.getId());
//        System.out.println(docRef.getId());
//        System.out.println(appHdrRef.getId());
//    }
//
//    private static Reference getReference(XMLSignatureFactory fac, String type, String id) throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
//
//        return switch (type) {
//            case "DOC" -> {
//
//                Reference docRef = fac.newReference(
//                         "",
//                        fac.newDigestMethod(DigestMethod.SHA256, null),
//                        Arrays.asList(
//                                fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
//                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
//                        ),
//                        null,
//                        id
//                    );
//                yield docRef;
//            }
//            case "KEYINFO" -> {
//
//                // Create references with explicit parameter specs
//                Reference keyInfoRef = fac.newReference(
//                        "#" + id,
//                        fac.newDigestMethod(DigestMethod.SHA256, null),
//                        Collections.singletonList(
//                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
//                        ),
//                        null,
//                        null
//                );
//                yield keyInfoRef;
//            }
//            case "HEADER" -> {
//                Reference appHdrRef = fac.newReference(
//                        "",
//                        fac.newDigestMethod(DigestMethod.SHA256, null),
//                        Arrays.asList(
//                                fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
//                                fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null)
//                        ),
//                        null,
//                        id
//                );
//                yield appHdrRef;
//            }
//            default -> null;
//        };
//    }
//
//    private static KeyInfo getKeyInfo(XMLSignatureFactory fac, X509Certificate cert, String keyInfoId) throws KeyException {
//        // Create KeyInfo with RSAKeyValue and X509Certificate
//        KeyInfoFactory kif = fac.getKeyInfoFactory();
//
//        // Create RSAKeyValue
//        KeyValue kv = kif.newKeyValue(cert.getPublicKey());
//
//        // Create X509Data
//        X509Data xd = kif.newX509Data(Collections.singletonList(cert));
//
//        // Create KeyInfo with ID
//        KeyInfo ki = kif.newKeyInfo(Arrays.asList(kv,xd), keyInfoId);
//        return ki;
//    }
//
//
//    private static String getMessageId() {
//        //MYYYYMMDDbbbPAAAnnnnnn nnnnn
//        /*Pos. 01-01 – Prefix ‘M’
//• Pos. 02-09 - File creation date in
//format YYYYMMDD
//21
//• Pos. 10-12 - Participant ID (3 characters)
//• Pos. 13-13 - Message generation source (“P” if generated by a Participant, "R" generated by RNDPS)
//• Pos. 14-16 - Discretionary bank field (3 digit alpha numeric that can be used to serialize, identify specific use case, etc.
//• Pos. 17-27 - Message serial number (can be up to 11 numeric characters)*/
//        LocalDate ldt = LocalDate.now();
//        DateTimeFormatter f = DateTimeFormatter.ofPattern( "uuuuMMdd" );
//        String output = ldt.format(f);
//        String participantId = "CBA";
//        String bankField = "LKP";
//        String unique = String.valueOf(System.currentTimeMillis()).substring(0,11);
//        return String.format("M%s%sP%s%s",output, participantId, bankField, unique);
//
//    }
//
//    private static Element getAppHdrElement(Document doc) throws XPathExpressionException {
//        NodeList appHdrList = doc.getElementsByTagNameNS("urn:iso:std:iso:20022:tech:xsd:head.001.001.01", "AppHdr");
////        XPathFactory xPathfactory = XPathFactory.newInstance();
////        XPath xpath = xPathfactory.newXPath();
////        XPathExpression hdrExprAssertionID = xpath.compile("//*[local-name()='AppHdr']");
////        Node appHdrId = (Node) hdrExprAssertionID.evaluate(doc, XPathConstants.NODE);
////        NodeList appHdrList = appHdrId.getChildNodes();
//        if (appHdrList.getLength() == 0) {
//            throw new RuntimeException("AppHdr element not found");
//        }
//        return (Element) appHdrList.item(0);
//    }
//
//    private static Element getDocElement(Document doc) {
//        NodeList docList = doc.getElementsByTagNameNS("urn:iso:std:iso:20022:tech:xsd:camt.003.001.07", "Document");
////        Element appHdrList = doc.getElementById( "BusinessMessage");
////        System.out.println("Document " + docList.getLength() + " " +docList.item(0));
//        if (docList.getLength() == 0) {
//            throw new RuntimeException("Body element not found");
//        }
//        return (Element) docList.item(0);
//    }
//
//    public static String transformToString(Document doc) throws Exception {
//        TransformerFactory tf = TransformerFactory.newInstance();
//        Transformer trans = tf.newTransformer();
//        trans.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
//        trans.setOutputProperty(OutputKeys.INDENT, "yes");
//        trans.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
//        trans.setOutputProperty(OutputKeys.ENCODING, "ISO-8859-1");
//
//        StringWriter writer = new StringWriter();
//        trans.transform(new DOMSource(doc), new StreamResult(writer));
//        String signedXml = writer.toString();
//        signedXml = signedXml.replaceAll("&#13;", "");
//        return signedXml;
////        return writer.toString();
//    }
//
//    private String getHeaderXMLMessage() {
//        return """
//                <AppHdr xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01"></AppHdr>
//               """;
//    }
//
//    public static String getXMLMessage() {
//        String keyInfoId = UUID.randomUUID().toString();
//        String msgId = "CBAFRWRW" + System.currentTimeMillis();
//        String creDtTm = Instant.now().toString();
//
//        msgId = "CBAFRWRW1753711044158";
//        creDtTm = "2025-07-28T13:57:24.160745Z";
//        keyInfoId = "dc1162cc-81ec-4b93-983e-153c448668ed";
//
//        String xml = String.format("""
//            <BusinessMessage>
//                <AppHdr ID="app-id" xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01"></AppHdr>
//                <Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.003.001.07" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Id="doc-id">
//
//                      <GetAcct>
//
//                       <MsgHdr>
//
//                        <MsgId>CBAFRWRW1753711044158</MsgId>
//
//                        <CreDtTm>2025-07-28T13:57:24.160745Z</CreDtTm>
//
//                       </MsgHdr>
//
//                       <AcctQryDef>
//
//                        <AcctCrit>
//
//                         <NewCrit>
//
//                          <SchCrit>
//
//                           <AcctId>
//
//                            <EQ>
//
//                             <Othr>
//
//                              <Id>***********</Id>
//
//                             </Othr>
//
//                            </EQ>
//
//                           </AcctId>
//
//                          </SchCrit>
//
//                         </NewCrit>
//
//                        </AcctCrit>
//
//                       </AcctQryDef>
//
//                      </GetAcct>
//
//                     </Document>
//            </BusinessMessage>
//            """, msgId, creDtTm);
//
////        return "<BusinessMessage><AppHdr xmlns=\"urn:iso:std:iso:20022:tech:xsd:head.001.001.01\"></AppHdr></BusinessMessage>\n";
//        return xml;
//    }
//}