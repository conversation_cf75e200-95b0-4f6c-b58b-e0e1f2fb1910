package com.itconsortium.signature.model;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Component
public class TransactionLog {
    private Long id;
    private String transactionId;
    private String referenceNumber;
    private String conversationId;
    private String resultCode;
    private String resultDescription;
    private String transactionStatus;
    private Date created;
    private Date updated;
    private String transactionType;
    private String msisdn;
    private BigDecimal amount;
    private BigDecimal charge;

    public MobileMoneyCallback creatMobileMoneyCallback() {
        MobileMoneyCallback mobileMoneyCallback = new MobileMoneyCallback();
        mobileMoneyCallback.setNetworkTransactionId(this.transactionId);
        mobileMoneyCallback.setRefNo(this.referenceNumber);
        mobileMoneyCallback.setResponseCode(this.resultCode);
        mobileMoneyCallback.setResponseMessage(this.resultDescription);
        return mobileMoneyCallback;
    }
}
