package com.itconsortium.signature.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DebitCustomerRequest {
    private String refNo;
    private String msisdn;
    private String amount;
    private String narration;
    private String voucher;
    private String opId;
    private String opCode;
    private String securityCredential;
    private String thirdPartyId;
    private String password;
    private String username;
    private String sourceIp;


    public TransactionLog createTransactionLog() {
        TransactionLog transactionLog = new TransactionLog();
        transactionLog.setReferenceNumber(this.refNo);
        transactionLog.setMsisdn(this.msisdn);
        transactionLog.setAmount(new BigDecimal(this.amount).setScale(2, BigDecimal.ROUND_DOWN));
        transactionLog.setTransactionStatus(TransStatus.PENDING);
        transactionLog.setTransactionType("DEBIT");
        return transactionLog;
    }

//    public PromptTransactionRequest buildPromptTransactionRequest() {
//        PromptTransactionRequest promptTransactionRequest = new PromptTransactionRequest();
//        promptTransactionRequest.setAmount(this.amount);
//        promptTransactionRequest.setMsisdn(this.msisdn);
//        if (this.narration != null) {
//            if (this.narration.length() >= 30) {
//                promptTransactionRequest.setReference_message(this.narration.substring(0, 30));
//            } else {
//                promptTransactionRequest.setReference_message(this.narration);
//            }
//        }
//        promptTransactionRequest.setTransaction_type(TransactionType.DEBIT);
//        promptTransactionRequest.setReference_number(this.refNo);
//        promptTransactionRequest.setSourceIp(this.sourceIp);
//        promptTransactionRequest.setUsername(this.username);
//        promptTransactionRequest.setPassword(this.password);
//        return promptTransactionRequest;
//    }
}
