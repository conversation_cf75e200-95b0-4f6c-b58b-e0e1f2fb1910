package com.itconsortium.signature;

import com.itconsortium.signature.utils.ApplicationProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class SignatureApplication {

	public static void main(String[] args) {
		SpringApplication.run(SignatureApplication.class, args);
	}

    @Bean
    ApplicationProperties applicationProperties(){
        return new ApplicationProperties();
    }
}
