plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.3'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.itconsortium.signature'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	implementation 'org.apache.santuario:xmlsec:3.0.3' // or latest version

	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-aop:2.7.5'
//	implementation group: 'org.springframework.cloud', name: 'spring-cloud-aws-messaging', version: '2.2.1.RELEASE'
	implementation 'org.junit.jupiter:junit-jupiter:5.9.0'

//	implementation group: 'com.squareup.okhttp', name: 'okhttp', version: '2.4.0'
//	implementation 'org.apache.commons:commons-lang3:3.12.0'
//	implementation group: 'commons-codec', name: 'commons-codec', version: '1.10'
	implementation group: 'org.apache.ws.commons.axiom', name: 'axiom-impl', version: '1.2.19'
	implementation group: 'xerces', name: 'xercesImpl', version: '2.11.0'
//
	implementation group: 'com.j256.cloudwatchlogbackappender', name: 'cloudwatchlogbackappender', version: '1.11'
	implementation group: 'com.amazonaws', name: 'aws-java-sdk-core', version: '1.11.715'
	implementation group: 'com.amazonaws', name: 'aws-java-sdk-ec2', version: '1.11.710'
	implementation group: 'com.amazonaws', name: 'aws-java-sdk-logs', version: '1.11.710'
//	implementation group: 'ch.qos.logback', name: 'logback-classic', version: '1.2.3'
//	implementation group: 'ch.qos.logback', name: 'logback-core', version: '1.5.13'

}

tasks.named('test') {
	useJUnitPlatform()
}
